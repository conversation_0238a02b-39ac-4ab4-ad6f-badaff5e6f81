import * as React from 'react';
import { redirect } from 'next/navigation';

import { getAuthOrganizationContext } from '@workspace/auth/context';
import { replaceOrgSlug, routes } from '@workspace/routes';

export default async function InstagramContactsPage(): Promise<React.JSX.Element> {
  const ctx = await getAuthOrganizationContext();
  
  // Redirect to Instagram contacts page
  redirect(replaceOrgSlug(routes.dashboard.organizations.slug.instagram.Contacts, ctx.organization.slug));
  
  // This code will never run due to the redirect
  return <div>Redirecting...</div>;
}
