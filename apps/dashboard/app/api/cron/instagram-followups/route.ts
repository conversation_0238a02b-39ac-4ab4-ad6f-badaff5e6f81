import { NextRequest, NextResponse } from 'next/server';
import { processFollowUps } from '@workspace/instagram-bot';

/**
 * Cron job to process Instagram follow-ups
 * This endpoint should be called by a cron job every minute
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Check for authorization header
    const authHeader = req.headers.get('authorization');
    if (!authHeader || authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Process follow-ups
    await processFollowUps();

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error processing Instagram follow-ups:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
