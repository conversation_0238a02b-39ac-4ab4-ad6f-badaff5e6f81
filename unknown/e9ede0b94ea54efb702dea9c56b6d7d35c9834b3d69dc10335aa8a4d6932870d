import * as React from 'react';
import { Metadata } from 'next';
import { redirect } from 'next/navigation';

import { getAuthOrganizationContext } from '@workspace/auth/context';

import {
  Page,
  PageBody,
  PageHeader,
  PagePrimaryBar
} from '@workspace/ui/components/page';
import { OrganizationPageTitle } from '~/components/organizations/slug/organization-page-title';
import { AdminPromptForm } from '~/components/organizations/slug/instagram/admin-prompt/admin-prompt-form';
import { TransitionProvider } from '~/hooks/use-transition-context';
import { createTitle } from '~/lib/formatters';
import { getAdminPrompt } from '~/data/instagram/get-admin-prompt';

export const metadata: Metadata = {
  title: createTitle('Admin Prompt')
};

export default async function AdminPromptPage(): Promise<React.JSX.Element> {
  const ctx = await getAuthOrganizationContext();

  // Only allow SaaS admin (<EMAIL>) to access this page
  if (ctx.session.user.email !== '<EMAIL>') {
    redirect(`/organizations/${ctx.organization.slug}/instagram/prompts`);
  }

  const adminPrompt = await getAdminPrompt();

  return (
    <TransitionProvider>
      <Page>
        <PageHeader>
          <PagePrimaryBar>
            <OrganizationPageTitle
              title="Admin Prompt"
              info="Configure the core prompts for the Instagram bot"
            />
          </PagePrimaryBar>
        </PageHeader>
        <PageBody className="p-6">
          <div className="space-y-6">
            <AdminPromptForm adminPrompt={adminPrompt} />
          </div>
        </PageBody>
      </Page>
    </TransitionProvider>
  );
}
