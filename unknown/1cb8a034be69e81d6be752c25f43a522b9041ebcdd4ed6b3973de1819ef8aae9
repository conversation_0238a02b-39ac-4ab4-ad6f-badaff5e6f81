---
description: Applies general TypeScript coding standards and best practices across the project.
globs: **/*.{ts,tsx}
---
- Use TypeScript for all code; prefer types over interfaces.
- Write concise, technical TypeScript code with accurate examples.
- Use functional and declarative programming patterns; avoid classes.
- Prefer iteration and modularization over code duplication.
- Use descriptive variable names with auxiliary verbs (e.g., `isLoading`, `hasError`).
