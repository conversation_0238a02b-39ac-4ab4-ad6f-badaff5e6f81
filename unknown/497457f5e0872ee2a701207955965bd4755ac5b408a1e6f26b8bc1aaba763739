'use client';

import * as React from 'react';
import { useParams } from 'next/navigation';

import { InstagramStatisticsSummaryCard } from '~/components/organizations/slug/instagram/statistics/instagram-statistics-summary-card';

export default function InstagramStatisticsError({
  error
}: {
  error: Error & { digest?: string };
}): React.JSX.Element {
  const params = useParams();
  const organizationSlug = params.slug as string;

  return (
    <InstagramStatisticsSummaryCard
      summary={null}
      organizationSlug={organizationSlug}
      error={error.message || 'Failed to load Instagram statistics'}
    />
  );
}
