import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@workspace/database/client';

/**
 * Cleanup old Instagram follow-ups
 * This endpoint should be called by a cron job daily
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Check for authorization header
    const authHeader = req.headers.get('authorization');
    if (!authHeader || authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('Starting cleanup of old follow-ups...');

    // Get all organizations with auto cleanup enabled
    const organizationsWithCleanup = await prisma.organization.findMany({
      where: {
        InstagramSettings: {
          autoCleanupEnabled: true
        }
      },
      include: {
        InstagramSettings: {
          select: {
            followUpCleanupDays: true,
            autoCleanupEnabled: true
          }
        }
      }
    });

    let totalDeletedNewFollowUps = 0;
    let totalClearedLegacyFollowUps = 0;
    const cleanupResults: Array<{
      organizationId: string;
      organizationName: string;
      cleanupDays: number;
      deletedNewFollowUps: number;
      clearedLegacyFollowUps: number;
    }> = [];

    for (const organization of organizationsWithCleanup) {
      if (!organization.InstagramSettings) {
        continue; // Skip organizations without Instagram settings
      }

      const settings = organization.InstagramSettings;
      const cleanupDays = settings.followUpCleanupDays || 30;

      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - cleanupDays);

      console.log(`Cleaning up ${organization.name} (${organization.id}): ${cleanupDays} days, cutoff: ${cutoffDate.toISOString()}`);

      // Clean up new follow-ups for this organization
      const deletedNewFollowUps = await prisma.instagramFollowUp.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDate
          },
          status: {
            in: ['sent', 'failed']
          },
          InstagramContact: {
            organizationId: organization.id
          }
        }
      });

      // Clean up old legacy follow-ups from InstagramContact table
      // Only clear follow-ups that are completed (sent/failed) and old
      const oldContacts = await prisma.instagramContact.findMany({
        where: {
          organizationId: organization.id,
          OR: [
            {
              followUpTime1: {
                lt: cutoffDate
              },
              followUpStatus1: {
                in: ['sent', 'failed']
              }
            },
            {
              followUpTime2: {
                lt: cutoffDate
              },
              followUpStatus2: {
                in: ['sent', 'failed']
              }
            },
            {
              followUpTime3: {
                lt: cutoffDate
              },
              followUpStatus3: {
                in: ['sent', 'failed']
              }
            },
            {
              followUpTime4: {
                lt: cutoffDate
              },
              followUpStatus4: {
                in: ['sent', 'failed']
              }
            },
            {
              followUpTime5: {
                lt: cutoffDate
              },
              followUpStatus5: {
                in: ['sent', 'failed']
              }
            },
            {
              followUpTime6: {
                lt: cutoffDate
              },
              followUpStatus6: {
                in: ['sent', 'failed']
              }
            }
          ]
        },
        select: {
          id: true,
          followUpTime1: true,
          followUpStatus1: true,
          followUpTime2: true,
          followUpStatus2: true,
          followUpTime3: true,
          followUpStatus3: true,
          followUpTime4: true,
          followUpStatus4: true,
          followUpTime5: true,
          followUpStatus5: true,
          followUpTime6: true,
          followUpStatus6: true
        }
      });

      let clearedLegacyFollowUps = 0;

      for (const contact of oldContacts) {
        const updateData: Record<string, any> = {};

        // Check each follow-up slot
        for (let i = 1; i <= 6; i++) {
          const timeField = `followUpTime${i}` as keyof typeof contact;
          const statusField = `followUpStatus${i}` as keyof typeof contact;

          const time = contact[timeField] as Date | null;
          const status = contact[statusField] as string | null;

          if (time && time < cutoffDate && (status === 'sent' || status === 'failed')) {
            updateData[`followUpMessage${i}`] = null;
            updateData[`followUpTime${i}`] = null;
            updateData[`followUpStatus${i}`] = null;
            clearedLegacyFollowUps++;
          }
        }

        if (Object.keys(updateData).length > 0) {
          await prisma.instagramContact.update({
            where: { id: contact.id },
            data: updateData
          });
        }
      }

      const totalCleaned = deletedNewFollowUps.count + clearedLegacyFollowUps;
      totalDeletedNewFollowUps += deletedNewFollowUps.count;
      totalClearedLegacyFollowUps += clearedLegacyFollowUps;

      cleanupResults.push({
        organizationId: organization.id,
        organizationName: organization.name,
        cleanupDays,
        deletedNewFollowUps: deletedNewFollowUps.count,
        clearedLegacyFollowUps
      });

      console.log(`Cleanup completed for ${organization.name}:
      - Deleted ${deletedNewFollowUps.count} new follow-ups
      - Cleared ${clearedLegacyFollowUps} legacy follow-ups
      - Total cleaned: ${totalCleaned}
      - Cutoff date: ${cutoffDate.toISOString()}
      - Cleanup days: ${cleanupDays}`);
    }

    const grandTotal = totalDeletedNewFollowUps + totalClearedLegacyFollowUps;

    console.log(`Global cleanup summary:
    - Organizations processed: ${cleanupResults.length}
    - Total new follow-ups deleted: ${totalDeletedNewFollowUps}
    - Total legacy follow-ups cleared: ${totalClearedLegacyFollowUps}
    - Grand total cleaned: ${grandTotal}`);

    return NextResponse.json({
      success: true,
      summary: {
        organizationsProcessed: cleanupResults.length,
        totalNewFollowUpsDeleted: totalDeletedNewFollowUps,
        totalLegacyFollowUpsCleared: totalClearedLegacyFollowUps,
        grandTotal
      },
      results: cleanupResults
    });

  } catch (error) {
    console.error('Error cleaning up old follow-ups:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
