'use server';

import { revalidateTag } from 'next/cache';
import { z } from 'zod';

import { prisma } from '@workspace/database/client';
import { NotFoundError } from '@workspace/common/errors';

import { authOrganizationActionClient } from '~/actions/safe-action';
import {
  Caching,
  OrganizationCacheKey
} from '~/data/caching';
import { getConversationHistory } from '~/lib/instagram-client';

const syncConversationSchema = z.object({
  contactId: z.string().uuid()
});

export const syncConversation = authOrganizationActionClient
  .metadata({ actionName: 'syncConversation' })
  .schema(syncConversationSchema)
  .action(async ({ parsedInput, ctx }) => {
    // Find the contact and make sure it belongs to the organization
    const contact = await prisma.instagramContact.findFirst({
      where: {
        id: parsedInput.contactId,
        organizationId: ctx.organization.id
      },
      select: {
        id: true,
        instagramId: true,
        Organization: {
          select: {
            InstagramSettings: {
              select: {
                instagramToken: true
              }
            }
          }
        }
      }
    });

    if (!contact) {
      throw new NotFoundError('Contact not found');
    }

    const instagramToken = contact.Organization.InstagramSettings?.instagramToken;

    if (!instagramToken) {
      throw new Error('Instagram token not configured');
    }

    if (!contact.instagramId) {
      throw new Error('Contact Instagram ID not available');
    }

    try {
      // Get conversation history from Instagram
      const conversationHistory = await getConversationHistory(
        contact.instagramId,
        instagramToken
      );

      // Check if we have messages
      if (!conversationHistory.data ||
        !conversationHistory.data[0] ||
        !conversationHistory.data[0].messages ||
        !conversationHistory.data[0].messages.data) {
        return {
          success: true,
          messagesCount: 0
        };
      }

      const messages = conversationHistory.data[0].messages.data;
      let newMessagesCount = 0;

      // Save each message to database
      for (const message of messages) {
        // Check if message already exists
        const existingMessage = await prisma.instagramMessage.findFirst({
          where: {
            contactId: contact.id,
            messageId: message.id
          }
        });

        if (!existingMessage) {
          // Determine if message is from user
          const isFromUser = message.from?.id === contact.instagramId;

          // Check for attachments
          let mediaUrl;
          let mediaType;

          if (message.attachments && message.attachments.data && message.attachments.data.length > 0) {
            const attachment = message.attachments.data[0];
            if (attachment.image_data) {
              mediaUrl = attachment.image_data.url;
              mediaType = 'image';
            } else if (attachment.video_data) {
              mediaUrl = attachment.video_data.url;
              mediaType = 'video';
            } else if (attachment.file_url) {
              mediaUrl = attachment.file_url;
              mediaType = 'file';
            }
          }

          // Save message to database
          await prisma.instagramMessage.create({
            data: {
              contactId: contact.id,
              messageId: message.id,
              content: message.message || '',
              isFromUser,
              mediaUrl,
              mediaType,
              timestamp: new Date(parseInt(message.created_time) * 1000)
            }
          });

          newMessagesCount++;
        }
      }

      // Update message count
      await prisma.instagramContact.update({
        where: { id: contact.id },
        data: {
          messageCount: await prisma.instagramMessage.count({
            where: { contactId: contact.id }
          })
        }
      });

      // Revalidate cache
      revalidateTag(
        Caching.createOrganizationTag(
          OrganizationCacheKey.InstagramContact,
          ctx.organization.id,
          contact.id
        )
      );

      return {
        success: true,
        messagesCount: newMessagesCount
      };
    } catch (error) {
      console.error('Error syncing conversation:', error);
      throw new Error('Failed to sync conversation');
    }
  });
