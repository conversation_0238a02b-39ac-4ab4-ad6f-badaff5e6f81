import { NextRequest, NextResponse } from 'next/server';
import { auth } from '~/auth';
import { prisma } from '@workspace/database/client';

/**
 * Helper function to get organization from referer
 */
async function getOrganizationFromRequest(req: NextRequest) {
  const session = await auth();
  if (!session?.user?.id) {
    throw new Error('Unauthorized');
  }

  // Get the organization slug from the referer URL
  const referer = req.headers.get('referer') || '';
  const match = referer.match(/\/organizations\/([^/]+)\//i);
  const orgSlug = match ? match[1] : null;

  if (!orgSlug) {
    throw new Error('Organization not found');
  }

  // Get the organization ID from the slug
  const organization = await prisma.organization.findFirst({
    where: { slug: orgSlug }
  });

  if (!organization) {
    throw new Error('Organization not found');
  }

  // Check if the user has access to the organization
  const userMembership = await prisma.membership.findFirst({
    where: {
      organizationId: organization.id,
      userId: session.user.id,
    },
  });

  if (!userMembership) {
    throw new Error('Unauthorized');
  }

  return { organization, session };
}

/**
 * Get cleanup settings for the organization
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    const { organization } = await getOrganizationFromRequest(req);

    // Get Instagram settings which will include cleanup configuration
    const instagramSettings = await prisma.instagramSettings.findFirst({
      where: {
        organizationId: organization.id
      },
      select: {
        id: true,
        followUpCleanupDays: true,
        autoCleanupEnabled: true
      }
    });

    // Default values if no settings exist
    const defaultSettings = {
      followUpCleanupDays: 30,
      autoCleanupEnabled: true
    };

    return NextResponse.json({
      success: true,
      data: {
        followUpCleanupDays: instagramSettings?.followUpCleanupDays ?? defaultSettings.followUpCleanupDays,
        autoCleanupEnabled: instagramSettings?.autoCleanupEnabled ?? defaultSettings.autoCleanupEnabled
      }
    });

  } catch (error) {
    console.error('Error fetching cleanup settings:', error);
    if (error instanceof Error) {
      if (error.message === 'Unauthorized') {
        return NextResponse.json(
          { success: false, error: 'Unauthorized' },
          { status: 401 }
        );
      }
      if (error.message === 'Organization not found') {
        return NextResponse.json(
          { success: false, error: 'Organization not found' },
          { status: 404 }
        );
      }
    }
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Update cleanup settings for the organization
 */
export async function PATCH(req: NextRequest): Promise<Response> {
  try {
    const { organization } = await getOrganizationFromRequest(req);
    const { followUpCleanupDays, autoCleanupEnabled } = await req.json();

    // Validate input
    if (followUpCleanupDays !== undefined) {
      if (typeof followUpCleanupDays !== 'number' || followUpCleanupDays < 1 || followUpCleanupDays > 365) {
        return NextResponse.json(
          { success: false, error: 'Cleanup days must be between 1 and 365' },
          { status: 400 }
        );
      }
    }

    if (autoCleanupEnabled !== undefined && typeof autoCleanupEnabled !== 'boolean') {
      return NextResponse.json(
        { success: false, error: 'Auto cleanup enabled must be a boolean' },
        { status: 400 }
      );
    }

    // Get or create Instagram settings
    let instagramSettings = await prisma.instagramSettings.findFirst({
      where: {
        organizationId: organization.id
      }
    });

    const updateData: any = {};
    if (followUpCleanupDays !== undefined) {
      updateData.followUpCleanupDays = followUpCleanupDays;
    }
    if (autoCleanupEnabled !== undefined) {
      updateData.autoCleanupEnabled = autoCleanupEnabled;
    }

    if (instagramSettings) {
      // Update existing settings
      instagramSettings = await prisma.instagramSettings.update({
        where: { id: instagramSettings.id },
        data: updateData
      });
    } else {
      // Create new settings
      instagramSettings = await prisma.instagramSettings.create({
        data: {
          organizationId: organization.id,
          isBotEnabled: false,
          minResponseTime: 30,
          maxResponseTime: 60,
          messageDelayMin: 3,
          messageDelayMax: 5,
          ...updateData
        }
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        followUpCleanupDays: instagramSettings.followUpCleanupDays,
        autoCleanupEnabled: instagramSettings.autoCleanupEnabled
      }
    });

  } catch (error) {
    console.error('Error updating cleanup settings:', error);
    if (error instanceof Error) {
      if (error.message === 'Unauthorized') {
        return NextResponse.json(
          { success: false, error: 'Unauthorized' },
          { status: 401 }
        );
      }
      if (error.message === 'Organization not found') {
        return NextResponse.json(
          { success: false, error: 'Organization not found' },
          { status: 404 }
        );
      }
    }
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
