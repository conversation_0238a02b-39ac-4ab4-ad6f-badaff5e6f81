'use server';

import { z } from 'zod';
import { generateInstagramResponse } from '@workspace/instagram-bot';
import { buildTestPrompt } from '@workspace/instagram-bot/test-prompt-builder';
import { authOrganizationActionClient } from '../safe-action';

const testAiResponseSchema = z.object({
  message: z.string().min(1, 'Message is required'),
  conversationHistory: z.string().default('')
});

/**
 * Format AI response for test display in readable format
 */
function formatTestResponse(response: any): string {
  const parts: string[] = [];

  // Add main messages
  if (response.message1) parts.push(`Wiadomosc1: ${response.message1}`);
  if (response.message2) parts.push(`Wiadomosc2: ${response.message2}`);
  if (response.message3) parts.push(`Wiadomosc3: ${response.message3}`);
  if (response.message4) parts.push(`Wiadomosc4: ${response.message4}`);

  // If no numbered messages, use the main message field
  if (parts.length === 0 && response.message) {
    parts.push(`Wiadomosc1: ${response.message}`);
  }

  // Add stage information
  if (response.stage) {
    parts.push(`\nStage: ${response.stage}`);
  }

  // Add reason if disqualified
  if (response.reason) {
    parts.push(`Reason: ${response.reason}`);
  }

  // Add follow-ups
  if (response.followUps && Array.isArray(response.followUps) && response.followUps.length > 0) {
    parts.push(''); // Empty line before follow-ups
    response.followUps.forEach((followUp: any, index: number) => {
      parts.push(`Followup${index + 1}: ${followUp.message}`);
      parts.push(`Delay: ${followUp.delayHours} hours`);
      if (index < response.followUps.length - 1) {
        parts.push(''); // Empty line between follow-ups
      }
    });
  }

  return parts.join('\n');
}

/**
 * Format AI response for conversation history (without follow-ups)
 */
function formatConversationResponse(response: any): string {
  const parts: string[] = [];

  // Add main messages only (no follow-ups for conversation history)
  if (response.message1) parts.push(`Wiadomosc1: ${response.message1}`);
  if (response.message2) parts.push(`Wiadomosc2: ${response.message2}`);
  if (response.message3) parts.push(`Wiadomosc3: ${response.message3}`);
  if (response.message4) parts.push(`Wiadomosc4: ${response.message4}`);

  // If no numbered messages, use the main message field
  if (parts.length === 0 && response.message) {
    parts.push(`Wiadomosc1: ${response.message}`);
  }

  return parts.join('\n');
}

export const testAiResponse = authOrganizationActionClient
  .metadata({ actionName: 'testAiResponse' })
  .schema(testAiResponseSchema)
  .action(async ({ parsedInput, ctx }) => {

    try {
      // Format the conversation history with the new message
      const formattedHistory = parsedInput.conversationHistory
        ? `${parsedInput.conversationHistory}\nUser: ${parsedInput.message}`
        : `User: ${parsedInput.message}`;

      // Build test prompt using test settings
      const testPrompt = await buildTestPrompt(ctx.organization.id, ctx.session.user.id);

      // Generate AI response using the test prompt with caching disabled
      const response = await generateInstagramResponse({
        prompt: testPrompt,
        conversationHistory: formattedHistory,
        organizationId: ctx.organization.id,
        disableCache: true // Always disable cache for test environment
      });

      // Format the response for test display
      const formattedBotMessage = formatTestResponse(response);

      // Format the response for conversation history (without follow-ups)
      const conversationBotMessage = formatConversationResponse(response);

      return {
        success: true,
        botMessage: formattedBotMessage,
        updatedHistory: `${formattedHistory}\nJa: ${conversationBotMessage}`,
        fullResponse: response,
        debugInfo: {
          fullPrompt: testPrompt,
          conversationHistory: formattedHistory,
          thinking: response.thinking || null,
          rawResponse: response
        }
      };
    } catch (error) {
      console.error('Error testing AI response:', error);
      throw new Error('Failed to generate AI response. Please check your configuration.');
    }
  });
