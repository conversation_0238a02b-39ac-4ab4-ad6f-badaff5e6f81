import * as React from 'react';
import { Metadata } from 'next';
import { redirect } from 'next/navigation';

import { getAuthOrganizationContext } from '@workspace/auth/context';

import {
  Page,
  PageBody,
  PageHeader,
  PagePrimaryBar
} from '@workspace/ui/components/page';
import { OrganizationPageTitle } from '~/components/organizations/slug/organization-page-title';
import { BotStyleForm } from '~/components/organizations/slug/instagram/bot-styles/bot-style-form';
import { TransitionProvider } from '~/hooks/use-transition-context';
import { createTitle } from '~/lib/formatters';

export const metadata: Metadata = {
  title: createTitle('Create Bot Style')
};

export default async function CreateBotStylePage(): Promise<React.JSX.Element> {
  const ctx = await getAuthOrganizationContext();

  // Only allow SaaS admin (<EMAIL>) to access this page
  if (ctx.session.user.email !== '<EMAIL>') {
    redirect(`/organizations/${ctx.organization.slug}/instagram/prompts`);
  }

  return (
    <TransitionProvider>
      <Page>
        <PageHeader>
          <PagePrimaryBar>
            <OrganizationPageTitle
              title="Create Bot Style"
              info="Create a new communication style for your Instagram bot"
            />
          </PagePrimaryBar>
        </PageHeader>
        <PageBody className="p-6">
          <div className="space-y-6">
            <BotStyleForm
              mode="create"
            />
          </div>
        </PageBody>
      </Page>
    </TransitionProvider>
  );
}
