import * as React from 'react';
import { type Metadata } from 'next';
import { redirect } from 'next/navigation';

import { getAuthOrganizationContext } from '@workspace/auth/context';
import {
  Page,
  PageActions,
  PageBody,
  PageHeader,
  PagePrimaryBar
} from '@workspace/ui/components/page';

import { BotStylesTable } from '~/components/organizations/slug/instagram/bot-styles/bot-styles-table';
import { CreateBotStyleButton } from '~/components/organizations/slug/instagram/bot-styles/create-bot-style-button';
import { getBotStyles } from '~/data/instagram/get-bot-styles';
import { OrganizationPageTitle } from '~/components/organizations/slug/organization-page-title';
import { TransitionProvider } from '~/hooks/use-transition-context';
import { createTitle } from '~/lib/formatters';

export const metadata: Metadata = {
  title: createTitle('Bot Styles')
};

export default async function BotStylesPage(): Promise<React.JSX.Element> {
  const ctx = await getAuthOrganizationContext();

  // Only allow SaaS admin (<EMAIL>) to access this page
  if (ctx.session.user.email !== '<EMAIL>') {
    redirect(`/organizations/${ctx.organization.slug}/instagram/prompts`);
  }

  const botStyles = await getBotStyles(ctx.organization.id);

  return (
    <TransitionProvider>
      <Page>
        <PageHeader>
          <PagePrimaryBar>
            <OrganizationPageTitle
              title="Bot Styles"
              info="Manage your Instagram bot communication styles"
            />
            <PageActions>
              <CreateBotStyleButton />
            </PageActions>
          </PagePrimaryBar>
        </PageHeader>
        <PageBody className="p-6">
          <div className="space-y-6">
            <BotStylesTable botStyles={botStyles} />
          </div>
        </PageBody>
      </Page>
    </TransitionProvider>
  );
}
