import { NextRequest, NextResponse } from 'next/server';
import { auth } from '~/auth';
import { prisma } from '@workspace/database/client';

/**
 * Helper function to get organization from referer
 */
async function getOrganizationFromRequest(req: NextRequest) {
  const session = await auth();
  if (!session?.user?.id) {
    throw new Error('Unauthorized');
  }

  // Get the organization slug from the referer URL
  const referer = req.headers.get('referer') || '';
  const match = referer.match(/\/organizations\/([^/]+)\//i);
  const orgSlug = match ? match[1] : null;

  if (!orgSlug) {
    throw new Error('Organization not found');
  }

  // Get the organization ID from the slug
  const organization = await prisma.organization.findFirst({
    where: { slug: orgSlug }
  });

  if (!organization) {
    throw new Error('Organization not found');
  }

  // Check if the user has access to the organization
  const userMembership = await prisma.membership.findFirst({
    where: {
      organizationId: organization.id,
      userId: session.user.id,
    },
  });

  if (!userMembership) {
    throw new Error('Unauthorized');
  }

  return { organization, session };
}

/**
 * Manual cleanup of old follow-ups for a specific organization
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    const { organization } = await getOrganizationFromRequest(req);
    const { cleanupDays } = await req.json();

    // Validate cleanup days
    if (!cleanupDays || typeof cleanupDays !== 'number' || cleanupDays < 1 || cleanupDays > 365) {
      return NextResponse.json(
        { success: false, error: 'Cleanup days must be between 1 and 365' },
        { status: 400 }
      );
    }

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - cleanupDays);

    console.log(`Manual cleanup for ${organization.name}: ${cleanupDays} days, cutoff: ${cutoffDate.toISOString()}`);

    // Clean up new follow-ups
    const deletedNewFollowUps = await prisma.instagramFollowUp.deleteMany({
      where: {
        createdAt: {
          lt: cutoffDate
        },
        status: {
          in: ['sent', 'failed']
        },
        InstagramContact: {
          organizationId: organization.id
        }
      }
    });

    // Clean up legacy follow-ups
    const oldContacts = await prisma.instagramContact.findMany({
      where: {
        organizationId: organization.id,
        OR: [
          {
            followUpTime1: {
              lt: cutoffDate
            },
            followUpStatus1: {
              in: ['sent', 'failed']
            }
          },
          {
            followUpTime2: {
              lt: cutoffDate
            },
            followUpStatus2: {
              in: ['sent', 'failed']
            }
          },
          {
            followUpTime3: {
              lt: cutoffDate
            },
            followUpStatus3: {
              in: ['sent', 'failed']
            }
          },
          {
            followUpTime4: {
              lt: cutoffDate
            },
            followUpStatus4: {
              in: ['sent', 'failed']
            }
          },
          {
            followUpTime5: {
              lt: cutoffDate
            },
            followUpStatus5: {
              in: ['sent', 'failed']
            }
          },
          {
            followUpTime6: {
              lt: cutoffDate
            },
            followUpStatus6: {
              in: ['sent', 'failed']
            }
          }
        ]
      },
      select: {
        id: true,
        followUpTime1: true,
        followUpStatus1: true,
        followUpTime2: true,
        followUpStatus2: true,
        followUpTime3: true,
        followUpStatus3: true,
        followUpTime4: true,
        followUpStatus4: true,
        followUpTime5: true,
        followUpStatus5: true,
        followUpTime6: true,
        followUpStatus6: true
      }
    });

    let clearedLegacyFollowUps = 0;

    for (const contact of oldContacts) {
      const updateData: Record<string, any> = {};
      
      // Check each follow-up slot
      for (let i = 1; i <= 6; i++) {
        const timeField = `followUpTime${i}` as keyof typeof contact;
        const statusField = `followUpStatus${i}` as keyof typeof contact;
        
        const time = contact[timeField] as Date | null;
        const status = contact[statusField] as string | null;
        
        if (time && time < cutoffDate && (status === 'sent' || status === 'failed')) {
          updateData[`followUpMessage${i}`] = null;
          updateData[`followUpTime${i}`] = null;
          updateData[`followUpStatus${i}`] = null;
          clearedLegacyFollowUps++;
        }
      }
      
      if (Object.keys(updateData).length > 0) {
        await prisma.instagramContact.update({
          where: { id: contact.id },
          data: updateData
        });
      }
    }

    const totalCleaned = deletedNewFollowUps.count + clearedLegacyFollowUps;

    console.log(`Manual cleanup completed for ${organization.name}:
    - Deleted ${deletedNewFollowUps.count} new follow-ups
    - Cleared ${clearedLegacyFollowUps} legacy follow-ups
    - Total cleaned: ${totalCleaned}
    - Cutoff date: ${cutoffDate.toISOString()}
    - Cleanup days: ${cleanupDays}`);

    return NextResponse.json({
      success: true,
      cleaned: {
        newFollowUps: deletedNewFollowUps.count,
        legacyFollowUps: clearedLegacyFollowUps,
        total: totalCleaned
      },
      cutoffDate: cutoffDate.toISOString(),
      cleanupDays,
      organizationName: organization.name
    });

  } catch (error) {
    console.error('Error in manual cleanup:', error);
    if (error instanceof Error) {
      if (error.message === 'Unauthorized') {
        return NextResponse.json(
          { success: false, error: 'Unauthorized' },
          { status: 401 }
        );
      }
      if (error.message === 'Organization not found') {
        return NextResponse.json(
          { success: false, error: 'Organization not found' },
          { status: 404 }
        );
      }
    }
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
