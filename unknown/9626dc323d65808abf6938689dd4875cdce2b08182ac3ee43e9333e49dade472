import * as React from 'react';

import { searchParamsCache } from '~/components/organizations/slug/home/<USER>';
import { LeadGenerationCard } from '~/components/organizations/slug/home/<USER>';
import { getLeadGenerationData } from '~/data/home/<USER>';

export default async function LeadGenerationPage({
  searchParams
}: NextPageProps): Promise<React.JSX.Element> {
  const parsedSearchParams = await searchParamsCache.parse(searchParams);
  const data = await getLeadGenerationData(parsedSearchParams);

  return <LeadGenerationCard data={data} />;
}
