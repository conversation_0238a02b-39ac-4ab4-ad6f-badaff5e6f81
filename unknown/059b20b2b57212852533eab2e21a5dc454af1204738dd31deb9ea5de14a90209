import * as React from 'react';
import { type Metadata } from 'next';

import { getAuthOrganizationContext } from '@workspace/auth/context';
import {
  Page,
  PageBody,
  PageHeader,
  PagePrimaryBar
} from '@workspace/ui/components/page';

import { getPromptConfig } from '~/data/instagram/get-prompt-config';
import { getBotStyles } from '~/data/instagram/get-bot-styles';
import { PromptConfigForm } from '~/components/organizations/slug/instagram/prompts/prompt-config-form';
import { OrganizationPageTitle } from '~/components/organizations/slug/organization-page-title';
import { TransitionProvider } from '~/hooks/use-transition-context';
import { createTitle } from '~/lib/formatters';

export const metadata: Metadata = {
  title: createTitle('Prompts')
};

export default async function PromptManagementPage(): Promise<React.JSX.Element> {
  const ctx = await getAuthOrganizationContext();

  const [promptConfig, botStyles] = await Promise.all([
    getPromptConfig(),
    getBotStyles(ctx.organization.id)
  ]);

  return (
    <TransitionProvider>
      <Page>
        <PageHeader>
          <PagePrimaryBar>
            <OrganizationPageTitle
              title="Prompts"
              info="Configure how your Instagram bot communicates with leads"
            />
          </PagePrimaryBar>
        </PageHeader>
        <PageBody className="p-6">
          <div className="space-y-6">
            <PromptConfigForm promptConfig={promptConfig} botStyles={botStyles} />
          </div>
        </PageBody>
      </Page>
    </TransitionProvider>
  );
}
