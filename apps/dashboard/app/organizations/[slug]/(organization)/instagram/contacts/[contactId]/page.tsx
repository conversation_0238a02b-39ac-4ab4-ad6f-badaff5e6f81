import * as React from 'react';
import { notFound } from 'next/navigation';
import { unstable_noStore as noStore } from 'next/cache';

import { getInstagramContact } from '~/data/instagram/get-instagram-contact';
import { getInstagramMessages } from '~/data/instagram/get-instagram-messages';
import { InstagramConversation } from '~/components/organizations/slug/instagram/contacts/instagram-conversation';
import { InstagramContactHeader } from '~/components/organizations/slug/instagram/contacts/instagram-contact-header';

interface InstagramContactPageProps {
  params: {
    contactId: string;
  };
}

export default async function InstagramContactPage({
  params
}: InstagramContactPageProps): Promise<React.JSX.Element> {
  // Disable caching for this page
  noStore();

  // Properly await params to avoid Next.js warning
  const { contactId } = await Promise.resolve(params);

  const [contact, messages] = await Promise.all([
    getInstagramContact(contactId),
    getInstagramMessages(contactId)
  ]);

  if (!contact) {
    notFound();
  }

  return (
    <div className="flex flex-col h-full gap-6 p-6">
      <InstagramContactHeader contact={contact} />
      <div className={`flex-1 ${contact.followUps.length > 0 ? 'min-h-[400px]' : 'min-h-0'}`}>
        <InstagramConversation contact={contact} messages={messages} />
      </div>
    </div>
  );
}
