import * as React from 'react';
import { Metadata } from 'next';

import { getAuthOrganizationContext } from '@workspace/auth/context';
import { TransitionProvider } from '~/hooks/use-transition-context';
import {
  Page,
  PageBody,
  PageHeader,
  PagePrimaryBar
} from '@workspace/ui/components/page';

import { OrganizationPageTitle } from '~/components/organizations/slug/organization-page-title';
import { InstagramStatisticsContainer } from '~/components/organizations/slug/instagram/statistics/instagram-statistics-container';

export const metadata: Metadata = {
  title: 'Instagram Statistics',
  description: 'View comprehensive Instagram bot performance metrics and analytics'
};

export default async function InstagramStatisticsPage(): Promise<React.JSX.Element> {
  const ctx = await getAuthOrganizationContext();

  return (
    <TransitionProvider>
      <Page>
        <PageHeader>
          <PagePrimaryBar>
            <OrganizationPageTitle
              title="Instagram Statistics"
              info="Comprehensive analytics and performance metrics for your Instagram bot"
            />
          </PagePrimaryBar>
        </PageHeader>
        <PageBody className="p-6">
          <InstagramStatisticsContainer />
        </PageBody>
      </Page>
    </TransitionProvider>
  );
}
