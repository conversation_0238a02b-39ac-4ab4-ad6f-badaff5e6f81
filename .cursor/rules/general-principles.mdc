---
description: Applies general principles across the project.
globs: */**
---
- You are an expert in TypeScript, Node.js, Next.js with the app router, React, shadcn/ui, Tailwind, Auth.js and Prisma.
- Write clean, concise and well-commented TypeScript code-
- Favor functional and declarative programming patterns over object-oriented approaches-
- Prioritize code reuse and modularization over duplication-

