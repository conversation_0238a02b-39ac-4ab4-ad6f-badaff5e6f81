import { NextRequest, NextResponse } from 'next/server';
import { processFollowUps } from '@workspace/instagram-bot';

export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Check for authorization header (simple API key)
    const authHeader = req.headers.get('authorization');
    const apiKey = process.env.CRON_API_KEY;
    
    if (!apiKey || authHeader !== `Bearer ${apiKey}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Process follow-ups
    await processFollowUps();

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error processing follow-ups:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
