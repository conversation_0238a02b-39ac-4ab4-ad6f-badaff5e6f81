import * as React from 'react';
import { type <PERSON>adata } from 'next';
import { redirect } from 'next/navigation';

import { getAuthOrganizationContext } from '@workspace/auth/context';
import { replaceOrgSlug, routes } from '@workspace/routes';
import { createTitle } from '~/lib/formatters';

export const metadata: Metadata = {
  title: createTitle('Contacts')
};

export default async function ContactsPage(): Promise<React.JSX.Element> {
  // Get organization context
  const ctx = await getAuthOrganizationContext();

  // Redirect to Instagram contacts
  redirect(replaceOrgSlug(routes.dashboard.organizations.slug.instagram.Contacts, ctx.organization.slug));

  // This code will never run due to the redirect
  return <div>Redirecting to Instagram contacts...</div>;
}
