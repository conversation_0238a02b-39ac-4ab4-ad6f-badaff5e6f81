'use server';

import { z } from 'zod';
import { revalidateTag } from 'next/cache';

import { prisma } from '@workspace/database/client';
import { NotFoundError } from '@workspace/common/errors';

import { authOrganizationActionClient } from '~/actions/safe-action';
import { Caching, OrganizationCacheKey } from '~/data/caching';

const schema = z.object({
  id: z.string()
});

export const deleteBotStyle = authOrganizationActionClient
  .metadata({ actionName: 'deleteBotStyle' })
  .schema(schema)
  .action(async ({ parsedInput, ctx }) => {
    // Only allow SaaS admin (<EMAIL>) to delete bot styles
    if (ctx.session.user.email !== '<EMAIL>') {
      throw new Error('Only SaaS admin can delete bot styles');
    }

    // Check if bot style exists
    const existingStyle = await prisma.botStyle.findUnique({
      where: {
        id: parsedInput.id
      }
    });

    if (!existingStyle) {
      throw new NotFoundError('Bot style not found');
    }

    // Don't allow deleting the default style
    if (existingStyle.isDefault) {
      throw new Error('Cannot delete the default bot style');
    }

    // Check if the style is in use
    const promptConfigCount = await prisma.promptConfig.count({
      where: {
        botStyleId: parsedInput.id
      }
    });

    if (promptConfigCount > 0) {
      throw new Error('Cannot delete a bot style that is in use');
    }

    // Delete the bot style
    await prisma.botStyle.delete({
      where: {
        id: parsedInput.id
      }
    });

    // Revalidate cache for both global and organization-specific caches
    revalidateTag(
      Caching.createOrganizationTag(
        OrganizationCacheKey.BotStyles,
        'global'
      )
    );

    revalidateTag(
      Caching.createOrganizationTag(
        OrganizationCacheKey.BotStyles,
        ctx.organization.id
      )
    );

    return { success: true };
  });
