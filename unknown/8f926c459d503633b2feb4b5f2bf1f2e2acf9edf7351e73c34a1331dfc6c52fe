import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@workspace/auth';
import { prisma } from '@workspace/database/client';
import { getBusinessAccountInfo } from '~/lib/instagram-client';

/**
 * Get Instagram business account information
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get organization from query params or headers
    const url = new URL(req.url);
    let orgSlug = url.searchParams.get('organizationId');

    if (!orgSlug) {
      const referer = req.headers.get('referer') || '';
      const match = referer.match(/\/organizations\/([^/]+)\//i);
      orgSlug = match ? match[1] : null;
    }

    if (!orgSlug) {
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }

    // Get the organization
    const organization = await prisma.organization.findFirst({
      where: { slug: orgSlug }
    });

    if (!organization) {
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }

    // Check if the user has access to the organization
    const userMembership = await prisma.membership.findFirst({
      where: {
        organizationId: organization.id,
        userId: session.user.id,
      },
    });

    if (!userMembership) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Get Instagram settings
    const instagramSettings = await prisma.instagramSettings.findFirst({
      where: {
        organizationId: organization.id,
        instagramToken: { not: null }
      }
    });

    if (!instagramSettings?.instagramToken) {
      return NextResponse.json({ 
        error: 'Instagram token not configured. Please configure your Instagram settings first.' 
      }, { status: 400 });
    }

    // Get business account info from Instagram API
    const businessInfo = await getBusinessAccountInfo(instagramSettings.instagramToken);

    return NextResponse.json({
      success: true,
      data: {
        id: businessInfo.id,
        username: businessInfo.username || '',
        name: businessInfo.name || '',
        profilePictureUrl: businessInfo.profile_picture_url,
        followersCount: businessInfo.followers_count || 0,
        mediaCount: businessInfo.media_count || 0,
        accountType: businessInfo.account_type || 'BUSINESS',
        biography: businessInfo.biography,
        lastUpdated: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error fetching Instagram business info:', error);
    
    // Handle specific Instagram API errors
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as any;
      if (axiosError.response?.status === 400) {
        return NextResponse.json({ 
          error: 'Invalid Instagram token or insufficient permissions. Please check your Instagram Business API setup.' 
        }, { status: 400 });
      }
      if (axiosError.response?.status === 401) {
        return NextResponse.json({ 
          error: 'Instagram token expired. Please reconnect your Instagram account.' 
        }, { status: 401 });
      }
    }
    
    return NextResponse.json({ 
      error: 'Failed to fetch Instagram business account information. Please try again later.' 
    }, { status: 500 });
  }
}
