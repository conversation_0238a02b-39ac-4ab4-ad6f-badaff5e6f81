import { NextRequest, NextResponse } from 'next/server';

import { prisma } from '@workspace/database/client';

export async function PATCH(
  req: NextRequest,
  { params }: { params: { contactId: string; followUpId: string } }
) {
  try {
    const { contactId, followUpId } = await Promise.resolve(params);
    const followUpNumber = parseInt(followUpId, 10);

    if (isNaN(followUpNumber) || followUpNumber < 1 || followUpNumber > 4) {
      return NextResponse.json({ error: 'Invalid follow-up ID' }, { status: 400 });
    }

    // Get the organization slug from the referer URL
    const referer = req.headers.get('referer') || '';
    const match = referer.match(/\/organizations\/([^/]+)\//i);
    const orgSlug = match ? match[1] : null;

    if (!orgSlug) {
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }

    // Get the organization ID from the slug
    const organization = await prisma.organization.findFirst({
      where: { slug: orgSlug }
    });

    if (!organization) {
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }

    // Check if contact exists and belongs to the organization
    const contact = await prisma.instagramContact.findFirst({
      where: {
        id: contactId,
        organizationId: organization.id
      }
    });

    if (!contact) {
      return NextResponse.json({ error: 'Contact not found' }, { status: 404 });
    }

    const { message, delayHours } = await req.json();

    if (!message || typeof message !== 'string') {
      return NextResponse.json({ error: 'Message is required' }, { status: 400 });
    }

    if (!delayHours || typeof delayHours !== 'number' || delayHours < 1) {
      return NextResponse.json({ error: 'Valid delay hours are required' }, { status: 400 });
    }

    // Calculate the new time based on the current time and delay
    const newTime = new Date();
    newTime.setHours(newTime.getHours() + delayHours);

    // Update the follow-up
    const updateData: Record<string, unknown> = {};
    updateData[`fu${followUpNumber}_message`] = message;
    updateData[`fu${followUpNumber}_time`] = newTime;

    // Only update status if it's not already sent
    const statusField = `fu${followUpNumber}_status`;
    if (contact[statusField as keyof typeof contact] !== 'sent') {
      updateData[statusField] = 'pending';
    }

    await prisma.instagramContact.update({
      where: { id: contactId },
      data: updateData
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error updating follow-up:', error);
    return NextResponse.json({ error: 'Failed to update follow-up' }, { status: 500 });
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: { contactId: string; followUpId: string } }
) {
  try {
    const { contactId, followUpId } = await Promise.resolve(params);
    const followUpNumber = parseInt(followUpId, 10);

    if (isNaN(followUpNumber) || followUpNumber < 1 || followUpNumber > 4) {
      return NextResponse.json({ error: 'Invalid follow-up ID' }, { status: 400 });
    }

    // Get the organization slug from the referer URL
    const referer = req.headers.get('referer') || '';
    const match = referer.match(/\/organizations\/([^/]+)\//i);
    const orgSlug = match ? match[1] : null;

    if (!orgSlug) {
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }

    // Get the organization ID from the slug
    const organization = await prisma.organization.findFirst({
      where: { slug: orgSlug }
    });

    if (!organization) {
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }

    // Check if contact exists and belongs to the organization
    const contact = await prisma.instagramContact.findFirst({
      where: {
        id: contactId,
        organizationId: organization.id
      }
    });

    if (!contact) {
      return NextResponse.json({ error: 'Contact not found' }, { status: 404 });
    }

    // Clear the follow-up
    const updateData: Record<string, unknown> = {};
    updateData[`fu${followUpNumber}_message`] = null;
    updateData[`fu${followUpNumber}_time`] = null;
    updateData[`fu${followUpNumber}_status`] = null;

    await prisma.instagramContact.update({
      where: { id: contactId },
      data: updateData
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting follow-up:', error);
    return NextResponse.json({ error: 'Failed to delete follow-up' }, { status: 500 });
  }
}
