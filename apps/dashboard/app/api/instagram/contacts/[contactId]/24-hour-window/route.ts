import { NextRequest, NextResponse } from 'next/server';

import { auth } from '~/auth';
import { prisma } from '@workspace/database/client';
import { isWithin24HourWindow, getLatest24HourTime } from '@workspace/instagram-bot';

/**
 * Get 24-hour window information for a contact
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { contactId: string } }
): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { contactId } = params;

    // Get the contact with organization membership check
    const contact = await prisma.instagramContact.findFirst({
      where: {
        id: contactId,
        User: {
          memberships: {
            some: {
              userId: session.user.id
            }
          }
        }
      },
      select: {
        id: true,
        instagramNickname: true,
        lastInteractionAt: true
      }
    });

    if (!contact) {
      return NextResponse.json(
        { success: false, error: 'Contact not found' },
        { status: 404 }
      );
    }

    const now = new Date();
    const latest24HourTime = getLatest24HourTime(contact.lastInteractionAt);
    const isCurrentlyWithinWindow = contact.lastInteractionAt ?
      isWithin24HourWindow(contact.lastInteractionAt, now) : false;

    // Calculate time remaining in window
    let timeRemainingMs = 0;
    if (latest24HourTime && isCurrentlyWithinWindow) {
      timeRemainingMs = latest24HourTime.getTime() - now.getTime();
    }

    return NextResponse.json({
      success: true,
      data: {
        contactId: contact.id,
        contactUsername: contact.instagramNickname,
        lastInteractionAt: contact.lastInteractionAt?.toISOString() || null,
        latest24HourTime: latest24HourTime?.toISOString() || null,
        isCurrentlyWithinWindow,
        timeRemainingMs,
        timeRemainingHours: timeRemainingMs > 0 ? Math.floor(timeRemainingMs / (1000 * 60 * 60)) : 0,
        timeRemainingMinutes: timeRemainingMs > 0 ? Math.floor((timeRemainingMs % (1000 * 60 * 60)) / (1000 * 60)) : 0
      }
    });
  } catch (error) {
    console.error('Error getting 24-hour window info:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
