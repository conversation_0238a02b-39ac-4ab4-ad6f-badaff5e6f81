import { NextRequest, NextResponse } from 'next/server';
import { auth } from '~/auth';
import { prisma } from '@workspace/database/client';

/**
 * Update Instagram settings for an organization
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await req.json();
    const { organizationId, settings } = body;

    if (!organizationId || !settings) {
      return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 });
    }

    // Check if the user has access to the organization
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userMembership = await prisma.membership.findFirst({
      where: {
        organizationId,
        userId: session.user.id,
      },
    });

    if (!userMembership) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Update Instagram settings
    const updatedSettings = await prisma.instagramSettings.update({
      where: {
        id: settings.id,
        organizationId,
      },
      data: {
        isBotEnabled: settings.isBotEnabled,
        instagramToken: settings.instagramToken,
        minResponseTime: settings.minResponseTime,
        maxResponseTime: settings.maxResponseTime,
      },
    });

    return NextResponse.json({ success: true, settings: updatedSettings });
  } catch (error) {
    console.error('Error updating Instagram settings:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
