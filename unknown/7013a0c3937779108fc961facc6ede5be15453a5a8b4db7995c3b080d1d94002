'use server';

import { revalidateTag } from 'next/cache';
import { z } from 'zod';

import { prisma } from '@workspace/database/client';

import { authOrganizationActionClient } from '~/actions/safe-action';
import {
  Caching,
  OrganizationCacheKey
} from '~/data/caching';

const saveTestSettingsSchema = z.object({
  id: z.string().uuid().optional(),
  aboutUs: z.string().optional(),
  qualificationQuestions: z.string().optional(),
  additionalInfo: z.string().optional(),
  botStyleId: z.string().uuid().optional(),
  youtubeLink: z.string().url().optional().or(z.literal('')),
  websiteLink: z.string().url().optional().or(z.literal('')),
  leadMagnetLink: z.string().url().optional().or(z.literal('')),
  conversionLink: z.string().url().optional().or(z.literal(''))
});

export const saveTestSettings = authOrganizationActionClient
  .metadata({ actionName: 'saveTestSettings' })
  .schema(saveTestSettingsSchema)
  .action(async ({ parsedInput, ctx }) => {
    const data = {
      organizationId: ctx.organization.id,
      userId: ctx.session.user.id,
      aboutUs: parsedInput.aboutUs || null,
      qualificationQuestions: parsedInput.qualificationQuestions || null,
      additionalInfo: parsedInput.additionalInfo || null,
      botStyleId: parsedInput.botStyleId || null,
      youtubeLink: parsedInput.youtubeLink || null,
      websiteLink: parsedInput.websiteLink || null,
      leadMagnetLink: parsedInput.leadMagnetLink || null,
      conversionLink: parsedInput.conversionLink || null
    };

    if (parsedInput.id) {
      // Update existing test settings
      await prisma.testSettings.update({
        where: { id: parsedInput.id },
        data,
        select: { id: true }
      });
    } else {
      // Create new test settings
      await prisma.testSettings.upsert({
        where: {
          organizationId_userId: {
            organizationId: ctx.organization.id,
            userId: ctx.session.user.id
          }
        },
        update: data,
        create: data,
        select: { id: true }
      });
    }

    // Revalidate cache
    revalidateTag(
      Caching.createOrganizationTag(
        OrganizationCacheKey.PromptConfig,
        ctx.organization.id
      )
    );
  });

/**
 * Copy production settings to test settings
 */
export const copyProductionToTestSettings = authOrganizationActionClient
  .metadata({ actionName: 'copyProductionToTestSettings' })
  .schema(z.object({}))
  .action(async ({ ctx }) => {
    console.log('Starting copy production to test settings for org:', ctx.organization.id, 'user:', ctx.session.user.id);

    try {
      // Get production prompt config
      const productionPromptConfig = await prisma.promptConfig.findFirst({
        where: { organizationId: ctx.organization.id },
        select: {
          aboutUs: true,
          qualificationQuestions: true,
          additionalInfo: true,
          botStyleId: true,
          youtubeLink: true,
          websiteLink: true,
          leadMagnetLink: true,
          conversionLink: true
        }
      });

      console.log('Found production prompt config:', !!productionPromptConfig);
      console.log('Production prompt config details:', productionPromptConfig);

      // Get production admin prompt
      const productionAdminPrompt = await prisma.adminPrompt.findFirst({
        select: {
          generalPrompt: true,
          technicalPrompt: true
        }
      });

      console.log('Found production admin prompt:', !!productionAdminPrompt);

      if (!productionPromptConfig && !productionAdminPrompt) {
        console.log('No production settings found to copy');
        throw new Error('No production settings found to copy');
      }

      // Validate botStyleId if it exists
      if (productionPromptConfig?.botStyleId) {
        console.log('Checking if botStyleId exists:', productionPromptConfig.botStyleId);
        const botStyleExists = await prisma.botStyle.findUnique({
          where: { id: productionPromptConfig.botStyleId },
          select: { id: true, name: true }
        });
        console.log('Bot style exists:', !!botStyleExists, botStyleExists);

        if (!botStyleExists) {
          console.warn('Bot style not found, setting botStyleId to null');
          productionPromptConfig.botStyleId = null;
        }
      }

      // Copy prompt config to test settings if it exists
      if (productionPromptConfig) {
        console.log('Copying production prompt config to test settings');
        try {
          await prisma.testSettings.upsert({
            where: {
              organizationId_userId: {
                organizationId: ctx.organization.id,
                userId: ctx.session.user.id
              }
            },
            update: {
              aboutUs: productionPromptConfig.aboutUs,
              qualificationQuestions: productionPromptConfig.qualificationQuestions,
              additionalInfo: productionPromptConfig.additionalInfo,
              botStyleId: productionPromptConfig.botStyleId,
              youtubeLink: productionPromptConfig.youtubeLink,
              websiteLink: productionPromptConfig.websiteLink,
              leadMagnetLink: productionPromptConfig.leadMagnetLink,
              conversionLink: productionPromptConfig.conversionLink || ''
            },
            create: {
              organizationId: ctx.organization.id,
              userId: ctx.session.user.id,
              aboutUs: productionPromptConfig.aboutUs,
              qualificationQuestions: productionPromptConfig.qualificationQuestions,
              additionalInfo: productionPromptConfig.additionalInfo,
              botStyleId: productionPromptConfig.botStyleId,
              youtubeLink: productionPromptConfig.youtubeLink,
              websiteLink: productionPromptConfig.websiteLink,
              leadMagnetLink: productionPromptConfig.leadMagnetLink,
              conversionLink: productionPromptConfig.conversionLink || ''
            }
          });
          console.log('Successfully copied production prompt config');
        } catch (error) {
          console.error('Error copying production prompt config:', error);
          throw error;
        }
      }

      // Copy admin prompt to test admin prompt if it exists
      if (productionAdminPrompt) {
        console.log('Copying production admin prompt to test admin prompt');
        try {
          await prisma.testAdminPrompt.upsert({
            where: {
              organizationId_userId: {
                organizationId: ctx.organization.id,
                userId: ctx.session.user.id
              }
            },
            update: {
              generalPrompt: productionAdminPrompt.generalPrompt,
              technicalPrompt: productionAdminPrompt.technicalPrompt
            },
            create: {
              organizationId: ctx.organization.id,
              userId: ctx.session.user.id,
              generalPrompt: productionAdminPrompt.generalPrompt,
              technicalPrompt: productionAdminPrompt.technicalPrompt
            }
          });
          console.log('Successfully copied production admin prompt');
        } catch (error) {
          console.error('Error copying production admin prompt:', error);
          throw error;
        }
      }

      // Revalidate multiple cache tags
      revalidateTag(
        Caching.createOrganizationTag(
          OrganizationCacheKey.PromptConfig,
          ctx.organization.id
        )
      );

      // Also revalidate test settings cache
      revalidateTag(
        Caching.createOrganizationTag(
          OrganizationCacheKey.PromptConfig,
          `test-${ctx.organization.id}-${ctx.session.user.id}`
        )
      );

      // Note: Anthropic cache is invalidated automatically, no manual invalidation needed

      console.log('Copy production to test settings completed successfully');
      return { success: true, message: 'Production settings copied successfully' };
    } catch (error) {
      console.error('Error in copyProductionToTestSettings:', error);
      console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace');
      throw new Error(`Failed to copy production settings: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  });