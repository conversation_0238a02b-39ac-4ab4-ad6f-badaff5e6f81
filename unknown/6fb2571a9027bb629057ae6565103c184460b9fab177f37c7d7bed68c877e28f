import * as React from 'react';

import { getAuthOrganizationContext } from '@workspace/auth/context';
import { getInstagramStatisticsSummary } from '~/data/instagram/get-instagram-statistics';
import { InstagramStatisticsSummaryCard } from '~/components/organizations/slug/instagram/statistics/instagram-statistics-summary-card';

export default async function InstagramStatisticsSlot(): Promise<React.JSX.Element> {
  const ctx = await getAuthOrganizationContext();

  try {
    const summary = await getInstagramStatisticsSummary();
    
    return (
      <InstagramStatisticsSummaryCard
        summary={summary}
        organizationSlug={ctx.organization.slug}
      />
    );
  } catch (error) {
    console.error('Error loading Instagram statistics summary:', error);
    
    return (
      <InstagramStatisticsSummaryCard
        summary={null}
        organizationSlug={ctx.organization.slug}
        error="Failed to load Instagram statistics"
      />
    );
  }
}
