import { NextRequest, NextResponse } from 'next/server';
import { verifyApiKey } from '@workspace/api-keys';
import { prisma } from '@workspace/database/client';

/**
 * Verify an API key
 * This endpoint is used by the Chrome extension to verify API keys
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    // Get API key from header
    const apiKey = req.headers.get('X-API-Key');
    
    if (!apiKey) {
      return NextResponse.json(
        { success: false, message: 'API key is missing' },
        { status: 401 }
      );
    }

    // Verify the API key
    const result = await verifyApiKey(apiKey);
    
    if (!result.success) {
      return NextResponse.json(
        { success: false, message: result.errorMessage },
        { status: 401 }
      );
    }

    // Get organization details
    const organization = await prisma.organization.findUnique({
      where: { id: result.organizationId },
      select: {
        id: true,
        name: true,
        slug: true
      }
    });

    if (!organization) {
      return NextResponse.json(
        { success: false, message: 'Organization not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        organizationId: organization.id,
        organizationName: organization.name,
        organizationSlug: organization.slug
      }
    });
  } catch (error) {
    console.error('Error verifying API key:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
