---
description: Enforces a consistent naming convention across the project.
globs: */**
---
- Use PascalCase for class names and type definitions.
- Utilize camelCase for variables, functions and methods.
- Employ kebab-case for file and directory names.
- Reserve UPPERCASE for environment variables and constants.
- Avoid magic numbers by defining constants with meaningful names.
- Start each function name with a verb to indicate its purpose.
