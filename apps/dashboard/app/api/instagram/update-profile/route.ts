import { NextRequest, NextResponse } from 'next/server';
import { auth } from '~/auth';
import { prisma } from '@workspace/database/client';
import { getProfile } from '~/lib/instagram-client';

/**
 * Update Instagram contact profile
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await req.json();
    const { organizationId, contactId } = body;

    if (!organizationId || !contactId) {
      return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 });
    }

    // Check if the user has access to the organization
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userMembership = await prisma.membership.findFirst({
      where: {
        organizationId,
        userId: session.user.id,
      },
    });

    if (!userMembership) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Find the contact
    const contact = await prisma.instagramContact.findUnique({
      where: {
        id: contactId,
        organizationId,
      },
    });

    if (!contact) {
      return NextResponse.json({ error: 'Contact not found' }, { status: 404 });
    }

    // Get Instagram settings
    const instagramSettings = await prisma.instagramSettings.findFirst({
      where: {
        organizationId,
        instagramToken: { not: null }
      }
    });

    if (!instagramSettings?.instagramToken) {
      return NextResponse.json({ error: 'Instagram token not found' }, { status: 400 });
    }

    if (!contact.instagramId) {
      return NextResponse.json({ error: 'Contact Instagram ID not available' }, { status: 400 });
    }

    // Get profile information from Instagram
    const profile = await getProfile(
      contact.instagramId,
      instagramSettings.instagramToken
    );

    // Update the contact with profile information
    const updatedContact = await prisma.instagramContact.update({
      where: {
        id: contactId
      },
      data: {
        instagramNickname: profile.username || profile.name || 'unknown',
        avatar: profile.profile_pic || null
      }
    });

    return NextResponse.json({
      success: true,
      contact: updatedContact
    });
  } catch (error) {
    console.error('Error updating Instagram contact profile:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
