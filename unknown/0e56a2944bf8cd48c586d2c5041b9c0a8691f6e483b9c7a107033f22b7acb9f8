'use server';

import { revalidateTag, revalidatePath } from 'next/cache';
import { z } from 'zod';

import { prisma } from '@workspace/database/client';

import { authOrganizationActionClient } from '~/actions/safe-action';
import {
  Caching,
  OrganizationCacheKey
} from '~/data/caching';

const saveInstagramSettingsSchema = z.object({
  id: z.string().uuid().optional(),
  isBotEnabled: z.boolean(),
  minResponseTime: z.number().min(5).max(120),
  maxResponseTime: z.number().min(5).max(120),
  messageDelayMin: z.number().min(1).max(30),
  messageDelayMax: z.number().min(1).max(30),
  instagramToken: z.string().min(1, 'Instagram token is required'),
  autoCleanupEnabled: z.boolean().optional(),
  followUpCleanupDays: z.number().min(1).max(365).optional()
}).refine(
  (data) => data.minResponseTime < data.maxResponseTime,
  {
    message: 'Minimum response time must be less than maximum response time',
    path: ['minResponseTime']
  }
).refine(
  (data) => data.messageDelayMin < data.messageDelayMax,
  {
    message: 'Minimum message delay must be less than maximum message delay',
    path: ['messageDelayMin']
  }
);

export const saveInstagramSettings = authOrganizationActionClient
  .metadata({ actionName: 'saveInstagramSettings' })
  .schema(saveInstagramSettingsSchema)
  .action(async ({ parsedInput, ctx }) => {
    // Check if settings already exist for this organization
    const existingSettings = await prisma.instagramSettings.findFirst({
      where: { organizationId: ctx.organization.id },
      select: { id: true }
    });

    if (existingSettings) {
      // Update existing settings
      await prisma.instagramSettings.update({
        where: { id: existingSettings.id },
        data: {
          isBotEnabled: parsedInput.isBotEnabled,
          minResponseTime: parsedInput.minResponseTime,
          maxResponseTime: parsedInput.maxResponseTime,
          messageDelayMin: parsedInput.messageDelayMin,
          messageDelayMax: parsedInput.messageDelayMax,
          instagramToken: parsedInput.instagramToken,
          autoCleanupEnabled: parsedInput.autoCleanupEnabled,
          followUpCleanupDays: parsedInput.followUpCleanupDays
        }
      });
    } else {
      // Create new settings
      await prisma.instagramSettings.create({
        data: {
          organizationId: ctx.organization.id,
          isBotEnabled: parsedInput.isBotEnabled,
          minResponseTime: parsedInput.minResponseTime,
          maxResponseTime: parsedInput.maxResponseTime,
          messageDelayMin: parsedInput.messageDelayMin,
          messageDelayMax: parsedInput.messageDelayMax,
          instagramToken: parsedInput.instagramToken,
          autoCleanupEnabled: parsedInput.autoCleanupEnabled,
          followUpCleanupDays: parsedInput.followUpCleanupDays
        }
      });
    }

    // Revalidate cache
    revalidateTag(
      Caching.createOrganizationTag(
        OrganizationCacheKey.InstagramSettings,
        ctx.organization.id
      )
    );

    // Revalidate the Instagram settings page path
    revalidatePath(`/organizations/${ctx.organization.slug}/instagram/settings`);

    return { success: true };
  });
