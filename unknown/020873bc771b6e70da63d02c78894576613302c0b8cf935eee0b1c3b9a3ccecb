import * as React from 'react';
import { Metadata } from 'next';
import { redirect } from 'next/navigation';

import { getAuthOrganizationContext } from '@workspace/auth/context';

import {
  Page,
  PageBody,
  PageHeader,
  PagePrimaryBar
} from '@workspace/ui/components/page';
import { OrganizationPageTitle } from '~/components/organizations/slug/organization-page-title';
import { TransitionProvider } from '~/hooks/use-transition-context';
import { createTitle } from '~/lib/formatters';
import { getAdminPrompt } from '~/data/instagram/get-admin-prompt';
import { getTestAdminPrompt } from '~/data/instagram/get-test-admin-prompt';
import { getTestSettings } from '~/data/instagram/get-test-settings';
import { getBotStyles } from '~/data/instagram/get-bot-styles';
import { getPromptConfig } from '~/data/instagram/get-prompt-config';
import { TestConversation } from '~/components/organizations/slug/instagram/test-settings/test-conversation';
import { TestAdminPromptForm } from '~/components/organizations/slug/instagram/test-settings/test-admin-prompt-form';
import { TestSettingsForm } from '~/components/organizations/slug/instagram/test-settings/test-settings-form';

export const metadata: Metadata = {
  title: createTitle('Test your settings')
};

export default async function TestSettingsPage(): Promise<React.JSX.Element> {
  const ctx = await getAuthOrganizationContext();

  const [adminPrompt, testAdminPrompt, testSettings, botStyles, promptConfig] = await Promise.all([
    getAdminPrompt(),
    getTestAdminPrompt(ctx.organization.id, ctx.session.user.id),
    getTestSettings(ctx.organization.id, ctx.session.user.id),
    getBotStyles(ctx.organization.id),
    getPromptConfig()
  ]);

  return (
    <TransitionProvider>
      <Page>
        <PageHeader>
          <PagePrimaryBar>
            <OrganizationPageTitle
              title="Test your settings"
              info="Test your Instagram bot configuration in a safe environment"
            />
          </PagePrimaryBar>
        </PageHeader>
        <PageBody className="p-6 space-y-6">
          {/* Full width conversation interface */}
          <div className="w-full">
            <TestConversation />
          </div>

          <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
            {/* Left side: Admin Prompts */}
            <div>
              <TestAdminPromptForm
                testAdminPrompt={testAdminPrompt}
                productionAdminPrompt={adminPrompt}
                userEmail={ctx.session.user.email}
              />
            </div>

            {/* Right side: Test Bot Configuration */}
            <div>
              <TestSettingsForm
                testSettings={testSettings}
                botStyles={botStyles}
                productionPromptConfig={promptConfig}
              />
            </div>
          </div>
        </PageBody>
      </Page>
    </TransitionProvider>
  );
}
