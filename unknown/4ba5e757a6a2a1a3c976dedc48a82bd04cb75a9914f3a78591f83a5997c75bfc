import * as React from 'react';

import { getAuthOrganizationContext } from '@workspace/auth/context';
import { InstagramStatisticsSummaryCard } from '~/components/organizations/slug/instagram/statistics/instagram-statistics-summary-card';

export default async function InstagramStatisticsLoading(): Promise<React.JSX.Element> {
  const ctx = await getAuthOrganizationContext();

  return (
    <InstagramStatisticsSummaryCard
      summary={null}
      organizationSlug={ctx.organization.slug}
      isLoading={true}
    />
  );
}
