import * as React from 'react';
import { Metadata } from 'next';
import { notFound, redirect } from 'next/navigation';

import { getAuthOrganizationContext } from '@workspace/auth/context';

import {
  Page,
  PageBody,
  PageHeader,
  PagePrimaryBar
} from '@workspace/ui/components/page';
import { OrganizationPageTitle } from '~/components/organizations/slug/organization-page-title';
import { BotStyleForm } from '~/components/organizations/slug/instagram/bot-styles/bot-style-form';
import { getBotStyle } from '~/data/instagram/get-bot-style';
import { TransitionProvider } from '~/hooks/use-transition-context';
import { createTitle } from '~/lib/formatters';

export const metadata: Metadata = {
  title: createTitle('Edit Bot Style')
};

interface EditBotStylePageProps {
  params: {
    id: string;
  };
}

export default async function EditBotStylePage({
  params
}: EditBotStylePageProps): Promise<React.JSX.Element> {
  const ctx = await getAuthOrganizationContext();

  // Only allow SaaS admin (<EMAIL>) to access this page
  if (ctx.session.user.email !== '<EMAIL>') {
    redirect(`/organizations/${ctx.organization.slug}/instagram/prompts`);
  }

  // Properly await params to avoid Next.js warning
  const { id } = await Promise.resolve(params);

  const botStyle = await getBotStyle(id);

  if (!botStyle) {
    notFound();
  }

  return (
    <TransitionProvider>
      <Page>
        <PageHeader>
          <PagePrimaryBar>
            <OrganizationPageTitle
              title="Edit Bot Style"
              info={`Update the "${botStyle.name}" communication style`}
            />
          </PagePrimaryBar>
        </PageHeader>
        <PageBody className="p-6">
          <div className="space-y-6">
            <BotStyleForm
              botStyle={botStyle}
              mode="edit"
            />
          </div>
        </PageBody>
      </Page>
    </TransitionProvider>
  );
}
