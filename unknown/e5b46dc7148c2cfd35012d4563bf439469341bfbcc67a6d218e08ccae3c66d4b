---
description: Enforces UI and styling conventions using Shadcn UI, Radix UI and Tailwind CSS for all components.
globs: **/*.{js,jsx,ts,tsx}
---
- Use Shadcn UI, Radix UI and Tailwind for components and styling.
- Make use of the components, hooks and utils available in `@workspace/ui/components`, `@workspace/ui/hooks` and `@workspace/ui/lib`.
- Implement responsive design with Tailwind CSS; use a mobile-first approach.
- Configure the Tailwind CSS theme in the `tooling/tailwind/index.js` file.
