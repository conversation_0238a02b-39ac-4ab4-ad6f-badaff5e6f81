'use server';

import { z } from 'zod';
import { revalidateTag } from 'next/cache';

import { prisma } from '@workspace/database/client';

import { authOrganizationActionClient } from '~/actions/safe-action';
import { Caching, OrganizationCacheKey } from '~/data/caching';

const schema = z.object({
  name: z.string().min(1),
  description: z.string().min(1),
  promptText: z.string().min(1),
  isDefault: z.boolean().default(false)
});

export const createBotStyle = authOrganizationActionClient
  .metadata({ actionName: 'createBotStyle' })
  .schema(schema)
  .action(async ({ parsedInput, ctx }) => {
    // Only allow SaaS admin (<EMAIL>) to create bot styles
    if (ctx.session.user.email !== '<EMAIL>') {
      throw new Error('Only SaaS admin can create bot styles');
    }

    // If this is set as default, unset any existing default
    if (parsedInput.isDefault) {
      await prisma.botStyle.updateMany({
        where: {
          isDefault: true
        },
        data: {
          isDefault: false
        }
      });
    }

    // Create the bot style
    const botStyle = await prisma.botStyle.create({
      data: {
        name: parsedInput.name,
        description: parsedInput.description,
        promptText: parsedInput.promptText,
        isDefault: parsedInput.isDefault
      }
    });

    // Revalidate cache for both global and organization-specific caches
    revalidateTag(
      Caching.createOrganizationTag(
        OrganizationCacheKey.BotStyles,
        'global'
      )
    );

    revalidateTag(
      Caching.createOrganizationTag(
        OrganizationCacheKey.BotStyles,
        ctx.organization.id
      )
    );

    return botStyle;
  });
